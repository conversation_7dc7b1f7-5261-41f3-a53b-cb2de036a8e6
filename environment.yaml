name: UAE
channels:
  - pyg
  - pytorch
  - nvidia
  - conda-forge
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - aiohttp=3.7.4.post0=py39h3811e60_1
  - apricot-select=0.6.1=pyhd8ed1ab_2
  - async-timeout=3.0.1=py_1000
  - attrs=25.3.0=pyh71513ae_0
  - blas=1.0=mkl
  - boost=1.74.0=py39h5472131_5
  - boost-cpp=1.74.0=h312852a_4
  - bottleneck=1.4.2=py39ha9d4c09_0
  - brotli-python=1.0.9=py39h6a678d5_9
  - bzip2=1.0.8=h5eee18b_6
  - ca-certificates=2025.6.15=hbd8a1cb_0
  - cairo=1.16.0=h6cf1ce9_1008
  - certifi=2025.6.15=pyhd8ed1ab_0
  - chardet=4.0.0=py39hf3d152e_3
  - charset-normalizer=3.3.2=pyhd3eb1b0_0
  - colorama=0.4.6=pyhd8ed1ab_1
  - contourpy=1.2.1=py39hdb19cb5_1
  - cuda-cudart=11.8.89=0
  - cuda-cupti=11.8.87=0
  - cuda-libraries=11.8.0=0
  - cuda-nvrtc=11.8.89=0
  - cuda-nvtx=11.8.86=0
  - cuda-runtime=11.8.0=0
  - cuda-version=12.9=3
  - cycler=0.12.1=pyhd8ed1ab_1
  - expat=2.7.1=h6a678d5_0
  - fcd_torch=1.0.7=pyh29332c3_2
  - filelock=3.17.0=py39h06a4308_0
  - fontconfig=2.14.0=h8e229c2_0
  - fonttools=4.55.3=py39h5eee18b_0
  - freetype=2.10.4=h5ab3b9f_0
  - fsspec=2025.5.1=pyhd8ed1ab_0
  - gmp=6.3.0=h6a678d5_0
  - gmpy2=2.2.1=py39h5eee18b_0
  - greenlet=3.1.1=py39h6a678d5_0
  - icu=68.2=h9c3ff4c_0
  - idna=3.7=py39h06a4308_0
  - importlib_resources=6.4.0=py39h06a4308_0
  - intel-openmp=2023.1.0=hdb19cb5_46306
  - jinja2=3.1.6=py39h06a4308_0
  - joblib=1.5.1=pyhd8ed1ab_0
  - jpeg=9e=h5eee18b_3
  - kiwisolver=1.4.4=py39h6a678d5_0
  - lcms2=2.16=h92b89f2_1
  - ld_impl_linux-64=2.40=h12ee557_0
  - lerc=4.0.0=h6a678d5_0
  - libcublas=11.11.3.6=0
  - libcufft=10.9.0.58=0
  - libcufile=1.14.1.1=4
  - libcurand=10.3.10.19=0
  - libcusolver=11.4.1.48=0
  - libcusparse=11.7.5.86=0
  - libdeflate=1.22=h5eee18b_0
  - libffi=3.4.4=h6a678d5_1
  - libgcc-ng=11.2.0=h1234567_1
  - libgfortran-ng=13.2.0=h69a702a_0
  - libgfortran5=13.2.0=ha4646dd_0
  - libglib=2.78.4=hdc74915_0
  - libgomp=11.2.0=h1234567_1
  - libiconv=1.16=h5eee18b_3
  - libllvm14=14.0.6=hecde1de_4
  - libnpp=11.8.0.86=0
  - libnvjpeg=11.9.0.86=0
  - libpng=1.6.39=h5eee18b_0
  - libstdcxx-ng=11.2.0=h1234567_1
  - libtiff=4.7.0=hde9077f_0
  - libuuid=2.32.1=h7f98852_1000
  - libwebp-base=1.3.2=h5eee18b_1
  - libxcb=1.17.0=h9b100fa_0
  - libxml2=2.9.12=h72842e0_0
  - lightning=2.3.3=pyhd8ed1ab_0
  - lightning-utilities=0.14.3=pyhd8ed1ab_0
  - llvmlite=0.43.0=py39h6a678d5_1
  - lz4-c=1.9.4=h6a678d5_1
  - markupsafe=3.0.2=py39h5eee18b_0
  - matplotlib-base=3.9.2=py39hbfdbfaf_1
  - mkl=2023.1.0=h213fc3f_46344
  - mkl-service=2.4.0=py39h5eee18b_2
  - mkl_fft=1.3.11=py39h5eee18b_0
  - mkl_random=1.2.8=py39h1128e8f_0
  - molsets=0.3.1=pyh29332c3_1
  - mpc=1.3.1=h5eee18b_0
  - mpfr=4.2.1=h5eee18b_0
  - mpmath=1.3.0=py39h06a4308_0
  - multidict=6.0.2=py39hb9d737c_1
  - ncurses=6.4=h6a678d5_0
  - networkx=3.2.1=py39h06a4308_0
  - nose=1.3.7=pyhd3eb1b0_1008
  - numba=0.60.0=py39h6a678d5_1
  - numexpr=2.10.1=py39h3c60e43_0
  - numpy=1.26.4=py39h5f9d8c6_0
  - numpy-base=1.26.4=py39hb5e798b_0
  - openbabel=3.1.1=py39hee2736e_2
  - openjpeg=2.5.2=h0d4d230_1
  - openssl=3.0.16=h5eee18b_0
  - overrides=7.4.0=py39h06a4308_0
  - packaging=24.2=pyhd8ed1ab_2
  - pandas=1.5.3=py39h417a72b_0
  - patsy=1.0.1=pyhd8ed1ab_1
  - pcre2=10.42=hebb0a14_1
  - pillow=11.1.0=py39hac6e08b_1
  - pip=25.1=pyhc872135_2
  - pixman=0.40.0=h7f8727e_1
  - pomegranate=1.0.0=pyhd8ed1ab_3
  - psutil=5.9.1=py39hb9d737c_0
  - pthread-stubs=0.3=h0ce48e5_1
  - pybind11-abi=4=hd3eb1b0_1
  - pycairo=1.23.0=py39h57c37a8_1
  - pyparsing=3.2.3=pyhd8ed1ab_1
  - pysocks=1.7.1=py39h06a4308_0
  - python=3.9.23=he99959a_0
  - python-dateutil=2.9.0post0=py39h06a4308_2
  - python_abi=3.9=2_cp39
  - pytorch=2.0.0=py3.9_cuda11.8_cudnn8.7.0_0
  - pytorch-cuda=11.8=h7e8668a_6
  - pytorch-lightning=2.3.3=pyhd8ed1ab_0
  - pytorch-mutex=1.0=cuda
  - pytorch-scatter=2.1.2=py39_torch_2.0.0_cu118
  - pytz=2025.2=pyhd8ed1ab_0
  - pyyaml=6.0.2=py39h5eee18b_0
  - rdkit=2020.09.5=py39hccf6a74_0
  - readline=8.2=h5eee18b_0
  - reportlab=3.6.13=py39h5eee18b_1
  - requests=2.32.3=py39h06a4308_1
  - scikit-learn=1.6.1=py39h6a678d5_0
  - scipy=1.13.1=py39h5f9d8c6_1
  - seaborn=0.13.2=hd8ed1ab_3
  - seaborn-base=0.13.2=pyhd8ed1ab_3
  - setuptools=78.1.1=py39h06a4308_0
  - six=1.17.0=py39h06a4308_0
  - sqlalchemy=2.0.39=py39h00e1ef3_0
  - sqlite=3.45.3=h5eee18b_0
  - statsmodels=0.14.4=py39h5eee18b_0
  - sympy=1.13.3=py39h06a4308_1
  - tbb=2021.8.0=hdb19cb5_0
  - threadpoolctl=3.6.0=pyhecae5ae_0
  - tk=8.6.14=h993c535_1
  - torch-geometric=2.6.1=pyhd8ed1ab_0
  - torchaudio=2.0.0=py39_cu118
  - torchmetrics=1.7.3=pyhd8ed1ab_0
  - torchtriton=2.0.0=py39
  - torchvision=0.15.2=cpu_py39h83e0c9b_0
  - tqdm=4.67.1=pyhd8ed1ab_1
  - typing-extensions=4.12.2=py39h06a4308_0
  - typing_extensions=4.12.2=py39h06a4308_0
  - tzdata=2025b=h04d1e81_0
  - unicodedata2=15.1.0=py39h5eee18b_1
  - urllib3=2.3.0=py39h06a4308_0
  - wheel=0.45.1=py39h06a4308_0
  - xorg-libice=1.0.10=h7f98852_0
  - xorg-libsm=1.2.3=hd9c2040_1000
  - xorg-libx11=1.8.12=h9b100fa_1
  - xorg-libxau=1.0.12=h9b100fa_0
  - xorg-libxdmcp=1.1.5=h9b100fa_0
  - xorg-libxext=1.3.6=h9b100fa_0
  - xorg-libxrender=0.9.12=h9b100fa_0
  - xorg-xorgproto=2024.1=h5eee18b_1
  - xz=5.6.4=h5eee18b_1
  - yaml=0.2.5=h7f98852_2
  - yarl=1.7.2=py39hb9d737c_2
  - zipp=3.21.0=py39h06a4308_0
  - zlib=1.2.13=h5eee18b_1
  - zstd=1.5.6=hc292b87_0
prefix: /home/<USER>/miniconda3/envs/UAE
