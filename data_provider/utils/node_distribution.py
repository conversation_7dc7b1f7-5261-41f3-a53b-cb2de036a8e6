from torch.distributions.categorical import Categorical
import torch


class DistributionNodes:
    def __init__(self, histogram):

        self.n_nodes = []
        prob = []
        self.keys = {}
        for i, nodes in enumerate(histogram):
            self.n_nodes.append(nodes)
            self.keys[nodes] = i
            prob.append(histogram[nodes])

        self.n_nodes = torch.tensor(self.n_nodes)

        prob = torch.tensor(prob)
        prob = prob / torch.sum(prob)

        self.prob = prob
        entropy = torch.sum(self.prob * torch.log(self.prob + 1e-30))
        # print("Entropy of n_nodes: H[N]", entropy.item())

        self.m = Categorical(prob)

    def sample(self, n_samples=1):
        idx = self.m.sample((n_samples,))
        return self.n_nodes[idx]

    def log_prob(self, batch_n_nodes):
        assert len(batch_n_nodes.size()) == 1

        idcs = [self.keys[i.item()] for i in batch_n_nodes]
        idcs = torch.tensor(idcs).to(batch_n_nodes.device)

        log_p = torch.log(self.prob + 1e-30)

        log_p = log_p.to(batch_n_nodes.device)

        log_probs = log_p[idcs]

        return log_probs


def get_node_dist(dataset_info):
    histogram = dataset_info['train_n_nodes']
    return DistributionNodes(histogram)
