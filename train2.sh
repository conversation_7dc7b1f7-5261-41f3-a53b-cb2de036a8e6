#!/bin/bash

export CUDA_VISIBLE_DEVICES=2

python latent_diffusion_trainer.py \
    --max_epochs=10000 \
    --filename='competition/UDM-default-uae6000' \
    --devices="[0,]" \
    --dataset='competition_with_h' \
    --root='data/competition' \
    --vae_ckpt='./all_checkpoints/competition/UAE/epoch=5999.ckpt' \
    --condition_property='properties' \
    # --noise_scheduler='poly' \
    # --latent_whiten='anisotropic' \
    


# --latent_whiten='anisotropic' \
