language: python

python:
  - "3.6"

before_install:
  - wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh -O miniconda.sh;
  - bash miniconda.sh -b -p $HOME/conda
  - export PATH="$HOME/conda/bin:$PATH"
  - conda config --set always_yes yes --set changeps1 no
  - conda update -q conda
  - conda create -c rdkit -n test_env python=$TRAVIS_PYTHON_VERSION pip cmake rdkit
  - source activate test_env

install:
  - conda install -q -c rdkit rdkit==2019.09.3.0
  - conda install -q flake8 scipy=1.2.0 pylint
  - python setup.py install

script:
  - flake8
  - pylint --disable=all --enable=no-else-return,unused-variable,wrong-import-order moses/ scripts/ tests/
  - python -m unittest discover -p "test_*.py" -v
