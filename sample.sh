#!/bin/bash

export CUDA_VISIBLE_DEVICES=5

echo "Sampling from the trained model..."

python latent_diffusion_trainer.py \
    --test_only \
    --filename='competition/UDM-default' \
    --ckpt_path='./all_checkpoints/competition/UDM-default/epoch=999.ckpt' \
    --devices="[0,]" \
    --dataset='competition_with_h' \
    --root='data/competition' \
    --vae_ckpt='./all_checkpoints/competition/UAE/last.ckpt' \
    --condition_property='properties' \
    --batch_size 100 \
    --split_dist \
    # --latent_whiten='anisotropic'
