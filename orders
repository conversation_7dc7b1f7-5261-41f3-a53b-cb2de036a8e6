df -h
du -h --max-depth=1 | sort -rh
find . -maxdepth 1 -type d -exec du -hs {} + | awk '$1 < "100M" {print $2}' | xargs rm -r
find . -type f | grep "datasets_gen_activity_cls.ipynb"
grep -r -n "six" --include=*.py .
grep -r -n -i "lfparity" --include=*.f90 .
kill $(lsof -i:6006|grep LISTEN|awk '{print $2}')
ps aux | grep lywen | grep python | grep main.py | awk '{print $2}' | xargs kill -15
nvidia-smi --query-compute-apps=pid --format=csv,noheader | wc -l


# conda create --name UAE-3D python=3.9
# conda activate UAE-3D
# pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
# pip install lightning
# pip install torch-geometric
# pip install rdkit==2022.03.4
# pip install fcd_torch
# pip install torch-scatter torch-sparse torch-cluster torch-spline-conv -f https://data.pyg.org/whl/
# pip show cython
# pip uninstall cython
# pip install "cython<3"
# pip install molsets

conda create --name UAE python=3.9.12
conda activate UAE
conda install pytorch==2.0.0 torchvision==0.15.0 torchaudio==2.0.0 pytorch-cuda=11.8 -c pytorch -c nvidia
conda install torch-geometric pandas lightning fcd_torch rdkit molsets overrides openbabel -c conda-forge --yes
conda install pytorch-scatter -c pyg --yes


python fusion_autoencoder_trainer.py --filename='QM9/UAE' --devices="[0,]" --dataset='qm9' --root='data/QM9'

python latent_diffusion_trainer.py --filename='QM9/UDM' --devices="[0,]" --dataset='qm9' --root='data/QM9' --vae_ckpt='./all_checkpoints/QM9/UAE/last.ckpt' --max_epochs=10000 --condition_property='mu'

python latent_diffusion_trainer.py --test_only --filename='QM9/UDM' --devices="[0,]" --dataset='qm9' --root='data/QM9' --vae_ckpt='./all_checkpoints/QM9/UAE/last.ckpt' --condition_property='mu'